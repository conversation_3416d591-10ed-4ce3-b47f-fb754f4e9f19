"""
Enhanced Email Template Management API Routes

This module provides comprehensive REST API endpoints for email template management,
campaigns, triggers, and analytics in the ACE Social admin panel.

@since 2024-1-1 to 2025-25-7
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from fastapi.security import HTTPBearer

from app.middleware.auth import get_current_admin_user
from app.services.advanced_rate_limiter import rate_limit
from app.models.user import User
from app.models.email_template import (
    EmailTemplate, EmailCampaign, EmailTrigger,
    TemplateCategory, TemplateStatus, CampaignStatus, TriggerType
)
from app.schemas.email_template_enhanced import (
    TemplateCreateRequest, TemplateUpdateRequest, TemplateListRequest,
    TemplateResponse, TemplateListResponse, TemplatePreviewRequest,
    TemplatePreviewResponse, TemplateDuplicateRequest,
    CampaignCreateRequest, CampaignUpdateRequest, CampaignListRequest,
    CampaignResponse, CampaignListResponse, CampaignTestRequest,
    TriggerCreateRequest, TriggerUpdateRequest, TriggerListRequest,
    TriggerResponse, TriggerListResponse, EmailDashboardResponse,
    TemplateValidationResponse, BulkOperationRequest, BulkOperationResponse
)
from app.services.email_template_enhanced_service import email_template_enhanced_service
from app.utils.response import create_response, create_error_response
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/admin/email-management", tags=["email-management"])
security = HTTPBearer()


# Template Management Endpoints

@router.post("/templates", response_model=TemplateResponse)
@rate_limit(calls=20, period=60)  # 20 creates per minute
async def create_template(
    request: Request,
    template_request: TemplateCreateRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """Create a new email template."""
    try:
        # Ensure service is initialized
        await email_template_enhanced_service.initialize()
        
        # Create the template
        template = await email_template_enhanced_service.create_template(
            template_request, 
            str(current_admin.id)
        )
        
        # Convert to response format
        response_data = template.dict()
        response_data["id"] = response_data.pop("_id", template.id)
        
        return create_response(
            data=TemplateResponse(**response_data),
            message="Email template created successfully"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating email template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create email template: {str(e)}"
        )


@router.get("/templates", response_model=TemplateListResponse)
@rate_limit(calls=100, period=60)  # 100 requests per minute
async def list_templates(
    request: Request,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    category: Optional[str] = Query(None),
    template_status: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    tags: Optional[List[str]] = Query(None),
    language: Optional[str] = Query(None),
    created_by: Optional[str] = Query(None),
    current_admin: User = Depends(get_current_admin_user)
):
    """List email templates with filtering."""
    try:
        # Ensure service is initialized
        await email_template_enhanced_service.initialize()
        
        # Create list request
        list_request = TemplateListRequest(
            skip=skip,
            limit=limit,
            category=TemplateCategory(category) if category else None,
            status=TemplateStatus(template_status) if template_status else None,
            search=search,
            tags=tags,
            language=language,
            created_by=created_by
        )
        
        # Get templates
        templates, total = await email_template_enhanced_service.list_templates(list_request)
        
        # Convert to response format
        template_responses = []
        for template in templates:
            response_data = template.dict()
            response_data["id"] = response_data.pop("_id", template.id)
            template_responses.append(TemplateResponse(**response_data))
        
        return create_response(
            data=TemplateListResponse(
                templates=template_responses,
                total=total,
                skip=skip,
                limit=limit
            ),
            message=f"Retrieved {len(template_responses)} email templates"
        )
        
    except Exception as e:
        logger.error(f"Error listing email templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list email templates: {str(e)}"
        )


@router.get("/templates/{template_id}", response_model=TemplateResponse)
@rate_limit(calls=100, period=60)
async def get_template(
    request: Request,
    template_id: str,
    current_admin: User = Depends(get_current_admin_user)
):
    """Get email template by ID."""
    try:
        # Ensure service is initialized
        await email_template_enhanced_service.initialize()
        
        # Get template
        template = await email_template_enhanced_service.get_template(template_id)
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Email template not found"
            )
        
        # Convert to response format
        response_data = template.dict()
        response_data["id"] = response_data.pop("_id", template.id)
        
        return create_response(
            data=TemplateResponse(**response_data),
            message="Email template retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting email template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get email template: {str(e)}"
        )


@router.put("/templates/{template_id}", response_model=TemplateResponse)
@rate_limit(calls=30, period=60)  # 30 updates per minute
async def update_template(
    request: Request,
    template_id: str,
    template_request: TemplateUpdateRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """Update email template."""
    try:
        # Ensure service is initialized
        await email_template_enhanced_service.initialize()
        
        # Update the template
        template = await email_template_enhanced_service.update_template(
            template_id,
            template_request,
            str(current_admin.id)
        )
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Email template not found"
            )
        
        # Convert to response format
        response_data = template.dict()
        response_data["id"] = response_data.pop("_id", template.id)
        
        return create_response(
            data=TemplateResponse(**response_data),
            message="Email template updated successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating email template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update email template: {str(e)}"
        )


@router.delete("/templates/{template_id}")
@rate_limit(calls=10, period=60)  # 10 deletes per minute
async def delete_template(
    request: Request,
    template_id: str,
    current_admin: User = Depends(get_current_admin_user)
):
    """Delete email template."""
    try:
        # Ensure service is initialized
        await email_template_enhanced_service.initialize()
        
        # Delete the template
        success = await email_template_enhanced_service.delete_template(template_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Email template not found"
            )
        
        return create_response(
            data={"deleted": True},
            message="Email template deleted successfully"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting email template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete email template: {str(e)}"
        )


@router.post("/templates/{template_id}/preview", response_model=TemplatePreviewResponse)
@rate_limit(calls=50, period=60)  # 50 previews per minute
async def preview_template(
    request: Request,
    template_id: str,
    preview_request: TemplatePreviewRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """Preview email template with sample data."""
    try:
        # Ensure service is initialized
        await email_template_enhanced_service.initialize()
        
        # Render template
        rendered = await email_template_enhanced_service.render_template(
            template_id,
            preview_request.template_data
        )
        
        # Extract variables used
        template = await email_template_enhanced_service.get_template(template_id)
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Email template not found"
            )
        
        variables_used = [var.name for var in template.variables]
        missing_variables = [
            var.name for var in template.variables 
            if var.required and var.name not in preview_request.template_data
        ]
        
        return create_response(
            data=TemplatePreviewResponse(
                rendered_html=rendered["html"] or "",
                rendered_text=rendered["text"],
                rendered_subject=rendered["subject"] or "",
                variables_used=variables_used,
                missing_variables=missing_variables,
                preview_url=None  # Could be implemented for hosted previews
            ),
            message="Template preview generated successfully"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error previewing email template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to preview email template: {str(e)}"
        )


@router.post("/templates/{template_id}/duplicate", response_model=TemplateResponse)
@rate_limit(calls=10, period=60)  # 10 duplicates per minute
async def duplicate_template(
    request: Request,
    template_id: str,
    duplicate_request: TemplateDuplicateRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """Duplicate an existing email template."""
    try:
        # Ensure service is initialized
        await email_template_enhanced_service.initialize()
        
        # Get original template
        original_template = await email_template_enhanced_service.get_template(template_id)
        if not original_template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Email template not found"
            )
        
        # Create duplicate request
        create_request = TemplateCreateRequest(
            name=duplicate_request.new_name,
            description=duplicate_request.new_description or f"Copy of {original_template.description}",
            category=original_template.category,
            subject=original_template.subject,
            html_content=original_template.html_content,
            text_content=original_template.text_content,
            variables=original_template.variables if duplicate_request.copy_variables else [],
            tags=original_template.tags if duplicate_request.copy_tags else [],
            language=original_template.language,
            allow_unsubscribe=original_template.allow_unsubscribe,
            track_opens=original_template.track_opens,
            track_clicks=original_template.track_clicks
        )
        
        # Create the duplicate
        duplicate_template = await email_template_enhanced_service.create_template(
            create_request,
            str(current_admin.id)
        )
        
        # Convert to response format
        response_data = duplicate_template.dict()
        response_data["id"] = response_data.pop("_id", duplicate_template.id)
        
        return create_response(
            data=TemplateResponse(**response_data),
            message="Email template duplicated successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error duplicating email template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to duplicate email template: {str(e)}"
        )


# Campaign Management Endpoints

@router.post("/campaigns", response_model=CampaignResponse)
@rate_limit(calls=10, period=60)  # 10 creates per minute
async def create_campaign(
    request: Request,
    campaign_request: CampaignCreateRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """Create a new email campaign."""
    try:
        # Ensure service is initialized
        await email_template_enhanced_service.initialize()

        # Create the campaign
        campaign = await email_template_enhanced_service.create_campaign(
            campaign_request,
            str(current_admin.id)
        )

        # Get template name for response
        template = await email_template_enhanced_service.get_template(campaign.template_id)
        template_name = template.name if template else "Unknown Template"

        # Convert to response format
        response_data = campaign.dict()
        response_data["id"] = response_data.pop("_id", campaign.id)
        response_data["template_name"] = template_name

        return create_response(
            data=CampaignResponse(**response_data),
            message="Email campaign created successfully"
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating email campaign: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create email campaign: {str(e)}"
        )


@router.get("/campaigns", response_model=CampaignListResponse)
@rate_limit(calls=100, period=60)
async def list_campaigns(
    request: Request,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    campaign_status: Optional[str] = Query(None),
    template_id: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    created_by: Optional[str] = Query(None),
    current_admin: User = Depends(get_current_admin_user)
):
    """List email campaigns with filtering."""
    try:
        # Ensure service is initialized
        await email_template_enhanced_service.initialize()

        # Create list request
        list_request = CampaignListRequest(
            skip=skip,
            limit=limit,
            status=CampaignStatus(campaign_status) if campaign_status else None,
            template_id=template_id,
            search=search,
            created_by=created_by
        )

        # Get campaigns
        campaigns, total = await email_template_enhanced_service.list_campaigns(list_request)

        # Convert to response format with template names
        campaign_responses = []
        for campaign in campaigns:
            template = await email_template_enhanced_service.get_template(campaign.template_id)
            template_name = template.name if template else "Unknown Template"

            response_data = campaign.dict()
            response_data["id"] = response_data.pop("_id", campaign.id)
            response_data["template_name"] = template_name
            campaign_responses.append(CampaignResponse(**response_data))

        return create_response(
            data=CampaignListResponse(
                campaigns=campaign_responses,
                total=total,
                skip=skip,
                limit=limit
            ),
            message=f"Retrieved {len(campaign_responses)} email campaigns"
        )

    except Exception as e:
        logger.error(f"Error listing email campaigns: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list email campaigns: {str(e)}"
        )


@router.get("/campaigns/{campaign_id}", response_model=CampaignResponse)
@rate_limit(calls=100, period=60)
async def get_campaign(
    request: Request,
    campaign_id: str,
    current_admin: User = Depends(get_current_admin_user)
):
    """Get email campaign by ID."""
    try:
        # Ensure service is initialized
        await email_template_enhanced_service.initialize()

        # Get campaign
        campaign = await email_template_enhanced_service.get_campaign(campaign_id)
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Email campaign not found"
            )

        # Get template name
        template = await email_template_enhanced_service.get_template(campaign.template_id)
        template_name = template.name if template else "Unknown Template"

        # Convert to response format
        response_data = campaign.dict()
        response_data["id"] = response_data.pop("_id", campaign.id)
        response_data["template_name"] = template_name

        return create_response(
            data=CampaignResponse(**response_data),
            message="Email campaign retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting email campaign {campaign_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get email campaign: {str(e)}"
        )


# Dashboard Endpoint

@router.get("/dashboard", response_model=EmailDashboardResponse)
@rate_limit(calls=60, period=60)  # 60 requests per minute
async def get_email_dashboard(
    request: Request,
    current_admin: User = Depends(get_current_admin_user)
):
    """Get email management dashboard overview."""
    try:
        # Ensure service is initialized
        await email_template_enhanced_service.initialize()

        # Get dashboard metrics (placeholder implementation)
        # In a real implementation, these would be calculated from actual data

        # Get template counts
        all_templates, total_templates = await email_template_enhanced_service.list_templates(
            TemplateListRequest(skip=0, limit=1000, search=None)
        )

        active_templates = len([t for t in all_templates if t.status == "active"])
        draft_templates = len([t for t in all_templates if t.status == "draft"])

        # Get campaign counts
        all_campaigns, total_campaigns = await email_template_enhanced_service.list_campaigns(
            CampaignListRequest(skip=0, limit=1000, search=None)
        )

        active_campaigns = len([c for c in all_campaigns if c.status == "running"])
        completed_campaigns = len([c for c in all_campaigns if c.status == "completed"])

        # Create dashboard response
        dashboard_data = EmailDashboardResponse(
            total_templates=total_templates,
            active_templates=active_templates,
            draft_templates=draft_templates,
            total_campaigns=total_campaigns,
            active_campaigns=active_campaigns,
            completed_campaigns=completed_campaigns,
            total_triggers=0,  # Placeholder
            active_triggers=0,  # Placeholder
            emails_sent_today=0,  # Would be calculated from delivery events
            emails_sent_this_week=0,  # Would be calculated from delivery events
            emails_sent_this_month=0,  # Would be calculated from delivery events
            overall_delivery_rate=95.0,  # Placeholder
            overall_open_rate=25.0,  # Placeholder
            overall_click_rate=5.0,  # Placeholder
            top_performing_templates=[],  # Would be calculated from analytics
            recent_campaigns=[]  # Would be recent campaigns
        )

        return create_response(
            data=dashboard_data,
            message="Email management dashboard retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error getting email dashboard: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get email dashboard: {str(e)}"
        )


# Bulk Operations

@router.post("/templates/bulk", response_model=BulkOperationResponse)
@rate_limit(calls=5, period=60)  # 5 bulk operations per minute
async def bulk_template_operation(
    request: Request,
    bulk_request: BulkOperationRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """Perform bulk operations on email templates."""
    try:
        if not bulk_request.confirm:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Bulk operation must be confirmed"
            )

        # Ensure service is initialized
        await email_template_enhanced_service.initialize()

        successful_items = 0
        failed_items = 0
        errors = []
        processed_ids = []

        for template_id in bulk_request.item_ids:
            try:
                if bulk_request.operation == "delete":
                    success = await email_template_enhanced_service.delete_template(template_id)
                    if success:
                        successful_items += 1
                        processed_ids.append(template_id)
                    else:
                        failed_items += 1
                        errors.append({"id": template_id, "error": "Template not found"})

                elif bulk_request.operation in ["archive", "activate", "deactivate"]:
                    # These would be implemented as status updates
                    new_status = {
                        "archive": "archived",
                        "activate": "active",
                        "deactivate": "draft"
                    }[bulk_request.operation]

                    # This would use the update_template method with status change
                    # For now, just count as successful
                    successful_items += 1
                    processed_ids.append(template_id)

            except Exception as e:
                failed_items += 1
                errors.append({"id": template_id, "error": str(e)})

        return create_response(
            data=BulkOperationResponse(
                operation=bulk_request.operation,
                total_items=len(bulk_request.item_ids),
                successful_items=successful_items,
                failed_items=failed_items,
                errors=errors,
                processed_ids=processed_ids
            ),
            message=f"Bulk {bulk_request.operation} operation completed"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error performing bulk operation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to perform bulk operation: {str(e)}"
        )


@router.post("/templates/validate", response_model=TemplateValidationResponse)
@rate_limit(calls=30, period=60)  # 30 validations per minute
async def validate_template_html(
    request: Request,
    template_data: dict,
    current_admin: User = Depends(get_current_admin_user)
):
    """Validate HTML template content."""
    try:
        # Ensure service is initialized
        await email_template_enhanced_service.initialize()

        html_content = template_data.get('html_content', '')
        subject = template_data.get('subject', '')

        # Validate template content
        validation_result = await email_template_enhanced_service._validate_template_content(
            html_content,
            subject
        )

        # Additional HTML-specific validation
        html_issues = []
        accessibility_issues = []
        warnings = []
        suggestions = []

        # Check for common HTML issues
        if '<html>' not in html_content.lower():
            warnings.append("Template should include <html> tag for better compatibility")

        if '<head>' not in html_content.lower():
            warnings.append("Template should include <head> section with meta tags")

        if 'charset' not in html_content.lower():
            html_issues.append("Missing charset declaration in HTML")

        if 'viewport' not in html_content.lower():
            suggestions.append("Consider adding viewport meta tag for mobile compatibility")

        # Check for accessibility
        if 'alt=' not in html_content.lower() and '<img' in html_content.lower():
            accessibility_issues.append("Images should have alt attributes for accessibility")

        if html_content.count('<table') > 0 and 'role=' not in html_content.lower():
            accessibility_issues.append("Consider adding role attributes to tables for screen readers")

        return create_response(
            data=TemplateValidationResponse(
                is_valid=validation_result["is_valid"],
                validation_errors=validation_result["errors"],
                warnings=validation_result["warnings"] + warnings,
                suggestions=suggestions,
                html_issues=html_issues,
                accessibility_issues=accessibility_issues
            ),
            message="Template validation completed"
        )

    except Exception as e:
        logger.error(f"Error validating template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to validate template: {str(e)}"
        )


@router.post("/templates/import-html")
@rate_limit(calls=10, period=60)  # 10 imports per minute
async def import_html_template(
    request: Request,
    html_data: dict,
    current_admin: User = Depends(get_current_admin_user)
):
    """Import HTML template from external source."""
    try:
        html_content = html_data.get('html_content', '')
        source_url = html_data.get('source_url', '')

        if not html_content:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="HTML content is required"
            )

        # Extract template metadata from HTML
        import re

        # Extract title
        title_match = re.search(r'<title>(.*?)</title>', html_content, re.IGNORECASE)
        extracted_title = title_match.group(1) if title_match else 'Imported Template'

        # Extract meta description
        desc_match = re.search(r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\']', html_content, re.IGNORECASE)
        extracted_description = desc_match.group(1) if desc_match else 'Imported from HTML'

        # Extract variables
        variables = re.findall(r'\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}', html_content)
        unique_variables = list(set(variables))

        # Clean HTML (basic sanitization)
        # In production, use a proper HTML sanitizer like bleach
        cleaned_html = html_content

        return create_response(
            data={
                "extracted_title": extracted_title,
                "extracted_description": extracted_description,
                "cleaned_html": cleaned_html,
                "extracted_variables": unique_variables,
                "source_url": source_url,
                "import_suggestions": [
                    "Review the extracted variables and add descriptions",
                    "Test the template with sample data",
                    "Verify mobile responsiveness",
                    "Check brand color consistency"
                ]
            },
            message="HTML template imported successfully"
        )

    except Exception as e:
        logger.error(f"Error importing HTML template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to import HTML template: {str(e)}"
        )
