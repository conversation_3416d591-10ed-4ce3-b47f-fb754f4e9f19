"""
Enhanced Knowledge Base Models for ACE Social Platform

This module provides comprehensive models for knowledge base management,
including articles, categories, templates, and analytics.

@since 2024-1-1 to 2025-25-7
"""
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from enum import Enum
from pydantic import BaseModel, Field, ConfigDict, validator
from bson import ObjectId

from app.models.user import PyObjectId, utc_now


class ArticleStatus(str, Enum):
    """Article publication status."""
    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"
    SCHEDULED = "scheduled"
    UNDER_REVIEW = "under_review"


class ArticleType(str, Enum):
    """Types of knowledge base articles."""
    HELP_ARTICLE = "help_article"
    BLOG_POST = "blog_post"
    ANNOUNCEMENT = "announcement"
    TECHNICAL_DOC = "technical_doc"
    FAQ = "faq"
    TUTORIAL = "tutorial"
    TROUBLESHOOTING = "troubleshooting"
    USER_GUIDE = "user_guide"
    API_DOCUMENTATION = "api_documentation"


class KnowledgeBaseCategory(str, Enum):
    """Knowledge base article categories."""
    FAQ = "faq"
    TUTORIALS = "tutorials"
    API_DOCUMENTATION = "api_documentation"
    USER_GUIDES = "user_guides"
    TROUBLESHOOTING = "troubleshooting"
    ANNOUNCEMENTS = "announcements"
    TECHNICAL = "technical"
    BILLING = "billing"
    ACCOUNT = "account"
    INTEGRATIONS = "integrations"
    FEATURES = "features"
    GENERAL = "general"


class DifficultyLevel(str, Enum):
    """Article difficulty levels."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class ContentFormat(str, Enum):
    """Content format types."""
    MARKDOWN = "markdown"
    HTML = "html"
    RICH_TEXT = "rich_text"


class ArticleRevision(BaseModel):
    """Model for article revision history."""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    revision_id: PyObjectId = Field(default_factory=PyObjectId)
    title: str
    content: str
    summary: Optional[str] = None
    author_id: PyObjectId
    author_name: str
    change_summary: str
    created_at: datetime = Field(default_factory=utc_now)
    version_number: int


class SEOMetadata(BaseModel):
    """SEO metadata for articles."""
    model_config = ConfigDict(
        validate_assignment=True,
        arbitrary_types_allowed=True
    )

    meta_title: Optional[str] = Field(None, max_length=60)
    meta_description: Optional[str] = Field(None, max_length=160)
    keywords: List[str] = Field(default_factory=list)
    slug: Optional[str] = Field(None, max_length=100)
    canonical_url: Optional[str] = None
    og_title: Optional[str] = None
    og_description: Optional[str] = None
    og_image: Optional[str] = None


class MediaAttachment(BaseModel):
    """Media attachments for articles."""
    file_id: PyObjectId
    file_name: str
    file_type: str  # image, video, document, etc.
    file_size: int
    file_url: str
    alt_text: Optional[str] = None
    caption: Optional[str] = None
    uploaded_at: datetime = Field(default_factory=utc_now)


class ArticleTemplate(BaseModel):
    """Template for creating articles."""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    name: str = Field(..., min_length=1, max_length=100)
    description: str
    article_type: ArticleType
    category: KnowledgeBaseCategory
    template_content: str
    variables: List[str] = Field(default_factory=list)  # Template variables like {{product_name}}
    is_active: bool = Field(default=True)
    usage_count: int = Field(default=0, ge=0)
    created_by: PyObjectId
    created_at: datetime = Field(default_factory=utc_now)
    updated_at: datetime = Field(default_factory=utc_now)


class ArticleAnalytics(BaseModel):
    """Analytics data for articles."""
    article_id: PyObjectId
    date: datetime
    views: int = Field(default=0, ge=0)
    unique_views: int = Field(default=0, ge=0)
    time_on_page: float = Field(default=0.0, ge=0.0)  # Average time in seconds
    bounce_rate: float = Field(default=0.0, ge=0.0, le=1.0)
    helpful_votes: int = Field(default=0, ge=0)
    not_helpful_votes: int = Field(default=0, ge=0)
    search_queries: List[str] = Field(default_factory=list)
    referrer_sources: Dict[str, int] = Field(default_factory=dict)


class KnowledgeBaseArticle(BaseModel):
    """Enhanced model for knowledge base articles."""
    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        json_schema_extra={
            "example": {
                "id": "64f8a1b2c3d4e5f6a7b8c9d0",
                "title": "How to Connect Your Instagram Account",
                "content": "Step-by-step guide to connect Instagram...",
                "summary": "Learn how to integrate your Instagram business account",
                "category": "tutorials",
                "article_type": "tutorial",
                "tags": ["instagram", "social-media", "setup"],
                "status": "published",
                "difficulty_level": "beginner",
                "is_featured": True,
                "view_count": 1250,
                "helpful_votes": 45,
                "not_helpful_votes": 3
            }
        }
    )

    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    
    # Core content
    title: str = Field(..., min_length=1, max_length=200)
    content: str = Field(..., min_length=1)
    summary: Optional[str] = Field(None, max_length=500)
    content_format: ContentFormat = Field(default=ContentFormat.RICH_TEXT)
    
    # Classification and organization
    category: KnowledgeBaseCategory = Field(default=KnowledgeBaseCategory.GENERAL)
    article_type: ArticleType = Field(default=ArticleType.HELP_ARTICLE)
    tags: List[str] = Field(default_factory=list)
    difficulty_level: DifficultyLevel = Field(default=DifficultyLevel.BEGINNER)
    
    # Publishing and status
    status: ArticleStatus = Field(default=ArticleStatus.DRAFT)
    is_featured: bool = Field(default=False)
    is_internal: bool = Field(default=False)  # Internal documentation vs public
    scheduled_publish_at: Optional[datetime] = None
    
    # Content management
    version: int = Field(default=1, ge=1)
    revisions: List[ArticleRevision] = Field(default_factory=list)
    template_id: Optional[PyObjectId] = None
    
    # SEO and discoverability
    seo: Optional[SEOMetadata] = None
    search_keywords: List[str] = Field(default_factory=list)
    related_articles: List[PyObjectId] = Field(default_factory=list)
    
    # Media and attachments
    featured_image: Optional[str] = None
    attachments: List[MediaAttachment] = Field(default_factory=list)
    
    # Analytics and engagement
    view_count: int = Field(default=0, ge=0)
    unique_view_count: int = Field(default=0, ge=0)
    helpful_votes: int = Field(default=0, ge=0)
    not_helpful_votes: int = Field(default=0, ge=0)
    average_rating: float = Field(default=0.0, ge=0.0, le=5.0)
    comment_count: int = Field(default=0, ge=0)
    
    # Authoring and workflow
    author_id: PyObjectId
    author_name: str
    reviewer_id: Optional[PyObjectId] = None
    reviewer_name: Optional[str] = None
    
    # Email integration
    email_template_variables: Dict[str, str] = Field(default_factory=dict)
    can_embed_in_emails: bool = Field(default=True)
    email_snippet: Optional[str] = None  # Short version for email inclusion
    
    # Timestamps
    created_at: datetime = Field(default_factory=utc_now)
    updated_at: datetime = Field(default_factory=utc_now)
    published_at: Optional[datetime] = None
    last_reviewed_at: Optional[datetime] = None
    
    @validator('slug', pre=True, always=True)
    def generate_slug(cls, v, values):
        """Generate URL-friendly slug from title if not provided."""
        if not v and 'title' in values:
            import re
            slug = re.sub(r'[^\w\s-]', '', values['title'].lower())
            slug = re.sub(r'[-\s]+', '-', slug)
            return slug.strip('-')
        return v
    
    @property
    def helpfulness_score(self) -> float:
        """Calculate helpfulness score based on votes."""
        total_votes = self.helpful_votes + self.not_helpful_votes
        if total_votes == 0:
            return 0.0
        return self.helpful_votes / total_votes
    
    @property
    def reading_time_minutes(self) -> int:
        """Estimate reading time in minutes (assuming 200 words per minute)."""
        word_count = len(self.content.split())
        return max(1, round(word_count / 200))


class KnowledgeBaseComment(BaseModel):
    """Comments on knowledge base articles."""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    article_id: PyObjectId
    user_id: Optional[PyObjectId] = None  # None for anonymous comments
    user_name: str
    user_email: Optional[str] = None
    content: str = Field(..., min_length=1, max_length=1000)
    is_helpful: Optional[bool] = None
    is_approved: bool = Field(default=False)
    is_internal: bool = Field(default=False)  # Internal team comments
    parent_comment_id: Optional[PyObjectId] = None  # For threaded comments
    created_at: datetime = Field(default_factory=utc_now)
    updated_at: datetime = Field(default_factory=utc_now)


class KnowledgeBaseSearch(BaseModel):
    """Search query tracking for analytics."""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    query: str
    user_id: Optional[PyObjectId] = None
    results_count: int = Field(default=0, ge=0)
    clicked_article_id: Optional[PyObjectId] = None
    session_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    created_at: datetime = Field(default_factory=utc_now)


class KnowledgeBaseSettings(BaseModel):
    """Global knowledge base settings."""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    
    # General settings
    site_title: str = Field(default="ACE Social Knowledge Base")
    site_description: str = Field(default="Help center and documentation")
    contact_email: str = Field(default="<EMAIL>")
    
    # Display settings
    articles_per_page: int = Field(default=20, ge=1, le=100)
    show_article_ratings: bool = Field(default=True)
    show_view_counts: bool = Field(default=True)
    enable_comments: bool = Field(default=True)
    require_comment_approval: bool = Field(default=True)
    
    # Search settings
    enable_search_suggestions: bool = Field(default=True)
    search_results_per_page: int = Field(default=10, ge=1, le=50)
    
    # Email integration
    include_kb_links_in_emails: bool = Field(default=True)
    auto_suggest_articles: bool = Field(default=True)
    
    # Analytics
    track_user_behavior: bool = Field(default=True)
    anonymize_analytics: bool = Field(default=True)
    
    # Timestamps
    updated_at: datetime = Field(default_factory=utc_now)
    updated_by: PyObjectId
