name: ACE Social Platform CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  # Memory optimization for builds
  NODE_OPTIONS: '--max-old-space-size=4096'

jobs:
  # ================================
  # Code Quality & Security Checks
  # ================================
  quality-checks:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'package-lock.json'

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: npm run install:all

    - name: Install Python linting tools
      run: |
        cd backend
        pip install flake8 black isort safety

    - name: Run ESLint
      run: npm run lint:frontend
      continue-on-error: true

    - name: Run Admin ESLint
      run: npm run lint:admin
      continue-on-error: true

    - name: Run Python linting
      run: npm run lint:backend
      continue-on-error: true

    - name: Security audit
      run: npm run security:audit
      continue-on-error: true

    - name: Validate build configuration
      run: npm run verify
      continue-on-error: true

    - name: Validate environment templates
      run: npm run verify:env
      continue-on-error: true

  # ================================
  # Unit & Integration Tests
  # ================================
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    needs: quality-checks
    
    services:
      mongodb:
        image: mongo:7.0
        env:
          MONGO_INITDB_ROOT_USERNAME: admin
          MONGO_INITDB_ROOT_PASSWORD: password123
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongosh --eval 'db.adminCommand(\"ping\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7.2-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'package-lock.json'

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: npm run install:all

    - name: Create test environment
      run: |
        # Create test environment from development template
        cp .env.development.template .env.test
        # Update environment-specific values for testing
        sed -i 's/ENVIRONMENT=development/ENVIRONMENT=test/g' .env.test
        sed -i 's/ace_social_dev/ace_social_test/g' .env.test
        sed -i 's/DEBUG=true/DEBUG=false/g' .env.test
        sed -i 's/LOG_LEVEL=DEBUG/LOG_LEVEL=INFO/g' .env.test
        # Copy backend environment
        cp backend/.env.example backend/.env.test
        sed -i 's/ENVIRONMENT=development/ENVIRONMENT=test/g' backend/.env.test
        sed -i 's/ace_social/ace_social_test/g' backend/.env.test

    - name: Run test suite
      run: npm run test:coverage
      env:
        CI: true
        ENVIRONMENT: test
        MONGODB_URL: ****************************************************************************
        REDIS_URL: redis://localhost:6379/1

    - name: Upload coverage reports
      uses: codecov/codecov-action@v4
      with:
        files: ./backend/coverage.xml,./frontend/coverage/lcov.info
        fail_ci_if_error: false
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      if: always() && (hashFiles('./backend/coverage.xml') != '' || hashFiles('./frontend/coverage/lcov.info') != '')

  # ================================
  # Build & Package
  # ================================
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [quality-checks, test]
    
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      image-tag: ${{ steps.meta.outputs.tags }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'package-lock.json'

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: npm run install:all

    - name: Install global dependencies
      run: |
        npm install -g cross-env
        cd frontend && npm install cross-env
        cd ../admin-app && npm install cross-env

    - name: Build application
      run: npm run build:production

    - name: Verify build output
      run: |
        ls -la frontend/dist/
        ls -la admin-app/dist/
        du -sh frontend/dist/
        du -sh admin-app/dist/
        echo "Frontend build size:"
        find frontend/dist -name "*.js" -exec du -ch {} + | grep total
        echo "Admin build size:"
        find admin-app/dist -name "*.js" -exec du -ch {} + | grep total

    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        build-args: |
          BUILD_DATE=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
          BUILD_VERSION=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.version'] }}
          BUILD_REVISION=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.revision'] }}

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-artifacts
        path: |
          frontend/dist/
          admin-app/dist/
          backend/
        retention-days: 7

  # ================================
  # End-to-End Tests
  # ================================
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'pull_request' || github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'package-lock.json'

    - name: Install dependencies
      run: |
        npm run install:all
        cd frontend && npx playwright install --with-deps

    - name: Start application
      run: npm run start:test

    - name: Wait for services
      run: |
        echo "Waiting for services to be ready..."
        timeout 60 bash -c 'until curl -f http://localhost:8000/health; do sleep 2; done'
        timeout 60 bash -c 'until curl -f http://localhost:3000; do sleep 2; done'

    - name: Run E2E tests
      run: npm run test:e2e
      env:
        CI: true

    - name: Upload E2E test results
      uses: actions/upload-artifact@v4
      if: always() && (hashFiles('frontend/test-results/**/*') != '' || hashFiles('frontend/playwright-report/**/*') != '')
      with:
        name: e2e-test-results
        path: |
          frontend/test-results/
          frontend/playwright-report/
        retention-days: 7

    - name: Stop application
      if: always()
      run: npm run stop:all

  # ================================
  # Security Scanning
  # ================================
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name != 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ needs.build.outputs.image-tag }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: javascript, python

    - name: Run CodeQL Analysis
      uses: github/codeql-action/analyze@v3

  # ================================
  # Deploy to Staging
  # ================================
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, test, e2e-tests]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    environment:
      name: staging
      url: https://staging.acesocial.com

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'package-lock.json'

    - name: Install dependencies
      run: npm run install:all

    - name: Deploy to staging
      run: npm run deploy:staging
      env:
        ENVIRONMENT: staging

    - name: Run smoke tests
      run: npm run health
      continue-on-error: true

    - name: Notify deployment
      if: always()
      run: |
        echo "Staging deployment completed with status: ${{ job.status }}"
        echo "Commit: ${{ github.event.head_commit.message }}"

  # ================================
  # Deploy to Production
  # ================================
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, test, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment:
      name: production
      url: https://acesocial.com

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'package-lock.json'

    - name: Install dependencies
      run: npm run install:all

    - name: Deploy to production
      run: npm run deploy:production
      env:
        ENVIRONMENT: production

    - name: Run health checks
      run: npm run health
      timeout-minutes: 5

    - name: Create release
      uses: softprops/action-gh-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ github.run_number }}
        name: ACE Social Platform v${{ github.run_number }}
        body: |
          🚀 **ACE Social Platform Release v${{ github.run_number }}**

          **Commit:** ${{ github.sha }}
          **Branch:** ${{ github.ref_name }}

          **Changes in this release:**
          ${{ github.event.head_commit.message }}

          **Build Information:**
          - Node.js: ${{ env.NODE_VERSION }}
          - Python: ${{ env.PYTHON_VERSION }}
          - Build Date: ${{ github.event.head_commit.timestamp }}

          **Deployment Status:** ✅ Successfully deployed to production
        draft: false
        prerelease: false

    - name: Notify deployment
      if: always()
      run: |
        echo "Production deployment completed with status: ${{ job.status }}"
        echo "Release: v${{ github.run_number }}"
        echo "Commit: ${{ github.event.head_commit.message }}"
