"""
Enhanced Email Template Service

This service manages email templates, campaigns, triggers, and analytics
with comprehensive functionality for the ACE Social platform.

@since 2024-1-1 to 2025-25-7
"""
import asyncio
import re
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta, timezone
# from motor.motor_asyncio import AsyncIOMotorDatabase  # Used at runtime only
from bson import ObjectId
import logging
from jinja2 import Template, Environment, BaseLoader, TemplateError

from app.core.config import settings
from app.core.database import get_database
from app.models.email_template import (
    EmailTemplate, EmailCampaign, EmailTrigger, EmailDeliveryEvent,
    TemplateAnalytics, UnsubscribeRecord, TemplateCategory, TemplateStatus,
    CampaignStatus, TriggerType, TemplateVariable, TemplateVersion
)
from app.schemas.email_template_enhanced import (
    TemplateCreateRequest, TemplateUpdateRequest, TemplateListRequest,
    CampaignCreateRequest, CampaignUpdateRequest, CampaignListRequest,
    TriggerCreateRequest, TriggerUpdateRequest, TriggerListRequest
)
from app.services.app_logging import get_logger

logger = get_logger(__name__)


class EmailTemplateEnhancedService:
    """Enhanced service for managing email templates and campaigns."""
    
    def __init__(self):
        self.db = None  # Union[AsyncIOMotorDatabase, None]
        self._template_cache = {}  # Dict[str, EmailTemplate]
        self._cache_expiry: datetime = datetime.now(timezone.utc)
        self._cache_duration = timedelta(minutes=10)
        self.jinja_env = Environment(loader=BaseLoader())

    async def initialize(self):
        """Initialize the service with database connection."""
        self.db = await get_database()
        await self._ensure_indexes()

    def _ensure_db(self):
        """Ensure database is initialized and return it."""
        if self.db is None:
            raise RuntimeError("EmailTemplateEnhancedService not initialized. Call initialize() first.")
        return self.db
    
    async def _ensure_indexes(self):
        """Ensure required database indexes exist."""
        try:
            db = self._ensure_db()
            # Email templates
            await db.email_templates.create_index([("name", 1)], unique=True)
            await db.email_templates.create_index([("category", 1)])
            await db.email_templates.create_index([("status", 1)])
            await db.email_templates.create_index([("tags", 1)])
            await db.email_templates.create_index([("created_by", 1)])
            await db.email_templates.create_index([("created_at", -1)])

            # Email campaigns
            await db.email_campaigns.create_index([("name", 1)])
            await db.email_campaigns.create_index([("template_id", 1)])
            await db.email_campaigns.create_index([("status", 1)])
            await db.email_campaigns.create_index([("created_at", -1)])

            # Email triggers
            await db.email_triggers.create_index([("event_name", 1)])
            await db.email_triggers.create_index([("trigger_type", 1)])
            await db.email_triggers.create_index([("template_id", 1)])
            await db.email_triggers.create_index([("is_active", 1)])

            # Email delivery events
            await db.email_delivery_events.create_index([("sent_at", -1)])
            await db.email_delivery_events.create_index([("template_id", 1)])
            await db.email_delivery_events.create_index([("campaign_id", 1)])
            await db.email_delivery_events.create_index([("recipient_email", 1)])

            # Unsubscribe records
            await db.unsubscribe_records.create_index([("email", 1)], unique=True)
            await db.unsubscribe_records.create_index([("unsubscribed_at", -1)])
            
            logger.info("Email template database indexes ensured")
        except Exception as e:
            logger.error(f"Error ensuring email template indexes: {str(e)}")
    
    # Template Management Methods
    
    async def create_template(
        self, 
        request: TemplateCreateRequest, 
        created_by: str
    ) -> EmailTemplate:
        """Create a new email template."""
        try:
            # Validate template content
            validation_result = await self._validate_template_content(
                request.html_content, 
                request.subject
            )
            
            if not validation_result["is_valid"]:
                raise ValueError(f"Template validation failed: {validation_result['errors']}")
            
            # Extract variables from content
            extracted_variables = self._extract_template_variables(
                request.html_content, 
                request.subject
            )
            
            # Merge with provided variables
            all_variables = self._merge_template_variables(
                extracted_variables, 
                request.variables
            )
            
            # Create template data
            template_data = {
                "name": request.name,
                "description": request.description,
                "category": request.category,
                "status": TemplateStatus.DRAFT,
                "subject": request.subject,
                "html_content": request.html_content,
                "text_content": request.text_content,
                "variables": [var.model_dump() for var in all_variables],
                "tags": request.tags,
                "language": request.language,
                "version": 1,
                "versions": [],
                "usage_count": 0,
                "is_system_template": False,
                "allow_unsubscribe": request.allow_unsubscribe,
                "track_opens": request.track_opens,
                "track_clicks": request.track_clicks,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "created_by": created_by
            }
            
            # Insert into database
            db = self._ensure_db()
            result = await db.email_templates.insert_one(template_data)
            template_data["_id"] = str(result.inserted_id)
            
            # Clear cache
            self._clear_cache()
            
            logger.info(f"Created email template: {request.name}")
            return EmailTemplate(**template_data)
            
        except Exception as e:
            logger.error(f"Error creating email template: {str(e)}")
            raise
    
    async def get_template(self, template_id: str) -> Optional[EmailTemplate]:
        """Get email template by ID."""
        try:
            # Check cache first
            if template_id in self._template_cache and self._is_cache_valid():
                return self._template_cache[template_id]
            
            # Query database
            db = self._ensure_db()
            template_data = await db.email_templates.find_one(
                {"_id": ObjectId(template_id)}
            )
            
            if not template_data:
                return None
            
            template_data["_id"] = str(template_data["_id"])
            template = EmailTemplate(**template_data)
            
            # Cache the result
            self._template_cache[template_id] = template
            
            return template
            
        except Exception as e:
            logger.error(f"Error getting email template {template_id}: {str(e)}")
            return None
    
    async def list_templates(
        self, 
        request: TemplateListRequest
    ) -> Tuple[List[EmailTemplate], int]:
        """List email templates with filtering and pagination."""
        try:
            # Build query filter
            query_filter = {}
            
            if request.category:
                query_filter["category"] = request.category
            
            if request.status:
                query_filter["status"] = request.status
            
            if request.tags:
                query_filter["tags"] = {"$in": request.tags}
            
            if request.language:
                query_filter["language"] = request.language
            
            if request.created_by:
                query_filter["created_by"] = request.created_by
            
            if request.search:
                query_filter["$or"] = [
                    {"name": {"$regex": request.search, "$options": "i"}},
                    {"description": {"$regex": request.search, "$options": "i"}},
                    {"subject": {"$regex": request.search, "$options": "i"}}
                ]
            
            # Get total count
            db = self._ensure_db()
            total = await db.email_templates.count_documents(query_filter)

            # Get templates with pagination
            cursor = db.email_templates.find(query_filter).sort("created_at", -1)
            cursor = cursor.skip(request.skip).limit(request.limit)
            
            templates = []
            async for template_data in cursor:
                template_data["_id"] = str(template_data["_id"])
                templates.append(EmailTemplate(**template_data))
            
            return templates, total
            
        except Exception as e:
            logger.error(f"Error listing email templates: {str(e)}")
            return [], 0
    
    async def update_template(
        self,
        template_id: str,
        request: TemplateUpdateRequest,
        updated_by: str
    ) -> Optional[EmailTemplate]:
        """Update email template."""
        try:
            # Get current template
            current_template = await self.get_template(template_id)
            if not current_template:
                return None
            
            # Build update data
            update_data = {"updated_at": datetime.now(timezone.utc), "updated_by": updated_by}
            
            # Create new version if content changed
            content_changed = False
            if request.html_content and request.html_content != current_template.html_content:
                content_changed = True
            if request.subject and request.subject != current_template.subject:
                content_changed = True
            
            if content_changed:
                # Create new version
                new_version = TemplateVersion(
                    version=current_template.version + 1,
                    html_content=request.html_content or current_template.html_content,
                    text_content=request.text_content or current_template.text_content,
                    subject=request.subject or current_template.subject,
                    variables=request.variables or current_template.variables,
                    created_at=datetime.now(timezone.utc),
                    created_by=updated_by,
                    change_notes="Template updated via API",
                    is_current=True
                )
                
                # Mark previous version as not current
                versions = current_template.versions.copy()
                for version in versions:
                    version.is_current = False
                versions.append(new_version)
                
                update_data["versions"] = [v.model_dump() for v in versions]
                update_data["version"] = new_version.version
            
            # Update other fields
            if request.name is not None:
                update_data["name"] = request.name
            if request.description is not None:
                update_data["description"] = request.description
            if request.category is not None:
                update_data["category"] = request.category
            if request.status is not None:
                update_data["status"] = request.status
            if request.html_content is not None:
                update_data["html_content"] = request.html_content
            if request.text_content is not None:
                update_data["text_content"] = request.text_content
            if request.variables is not None:
                update_data["variables"] = [var.model_dump() for var in request.variables]
            if request.tags is not None:
                update_data["tags"] = request.tags
            if request.language is not None:
                update_data["language"] = request.language
            if request.allow_unsubscribe is not None:
                update_data["allow_unsubscribe"] = request.allow_unsubscribe
            if request.track_opens is not None:
                update_data["track_opens"] = request.track_opens
            if request.track_clicks is not None:
                update_data["track_clicks"] = request.track_clicks
            
            # Update in database
            db = self._ensure_db()
            result = await db.email_templates.update_one(
                {"_id": ObjectId(template_id)},
                {"$set": update_data}
            )
            
            if result.matched_count == 0:
                return None
            
            # Clear cache
            self._clear_cache()
            
            # Return updated template
            return await self.get_template(template_id)
            
        except Exception as e:
            logger.error(f"Error updating email template {template_id}: {str(e)}")
            return None
    
    async def delete_template(self, template_id: str) -> bool:
        """Delete email template."""
        try:
            # Check if template is used in campaigns or triggers
            db = self._ensure_db()
            campaign_count = await db.email_campaigns.count_documents(
                {"template_id": template_id}
            )
            trigger_count = await db.email_triggers.count_documents(
                {"template_id": template_id}
            )
            
            if campaign_count > 0 or trigger_count > 0:
                raise ValueError(
                    f"Cannot delete template: used in {campaign_count} campaigns "
                    f"and {trigger_count} triggers"
                )
            
            result = await db.email_templates.delete_one(
                {"_id": ObjectId(template_id)}
            )
            
            if result.deleted_count > 0:
                # Clear cache
                self._clear_cache()
                logger.info(f"Deleted email template: {template_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error deleting email template {template_id}: {str(e)}")
            raise
    
    # Helper Methods
    
    def _extract_template_variables(self, html_content: str, subject: str) -> List[TemplateVariable]:
        """Extract variables from template content."""
        variables = []
        
        # Find all {{variable}} patterns
        pattern = r'\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}'
        
        # Extract from HTML content
        html_matches = re.findall(pattern, html_content)
        
        # Extract from subject
        subject_matches = re.findall(pattern, subject)
        
        # Combine and deduplicate
        all_matches = list(set(html_matches + subject_matches))
        
        for var_name in all_matches:
            variables.append(TemplateVariable(
                name=var_name,
                description=f"Variable: {var_name}",
                type="string",
                required=False,
                example_value=f"sample_{var_name}"
            ))
        
        return variables
    
    def _merge_template_variables(
        self, 
        extracted: List[TemplateVariable], 
        provided: List[TemplateVariable]
    ) -> List[TemplateVariable]:
        """Merge extracted and provided variables."""
        # Create a map of provided variables
        provided_map = {var.name: var for var in provided}
        
        # Start with provided variables
        merged = provided.copy()
        
        # Add extracted variables that aren't already provided
        for extracted_var in extracted:
            if extracted_var.name not in provided_map:
                merged.append(extracted_var)
        
        return merged
    
    async def _validate_template_content(self, html_content: str, subject: str) -> Dict[str, Any]:
        """Validate template content."""
        errors = []
        warnings = []
        
        try:
            # Test Jinja2 template compilation
            template = self.jinja_env.from_string(html_content)
            subject_template = self.jinja_env.from_string(subject)
            
            # Basic HTML validation
            if not html_content.strip():
                errors.append("HTML content cannot be empty")
            
            if not subject.strip():
                errors.append("Subject cannot be empty")
            
            # Check for basic HTML structure
            if '<html>' not in html_content.lower() and '<body>' not in html_content.lower():
                warnings.append("Template should include basic HTML structure")
            
            return {
                "is_valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings
            }
            
        except TemplateError as e:
            errors.append(f"Template syntax error: {str(e)}")
            return {
                "is_valid": False,
                "errors": errors,
                "warnings": warnings
            }
    
    def _is_cache_valid(self) -> bool:
        """Check if template cache is still valid."""
        return datetime.now(timezone.utc) < self._cache_expiry
    
    def _clear_cache(self):
        """Clear template cache."""
        self._template_cache.clear()
        self._cache_expiry = datetime.now(timezone.utc)


    # Campaign Management Methods

    async def create_campaign(
        self,
        request: CampaignCreateRequest,
        created_by: str
    ) -> EmailCampaign:
        """Create a new email campaign."""
        try:
            # Validate template exists
            template = await self.get_template(request.template_id)
            if not template:
                raise ValueError(f"Template {request.template_id} not found")

            # Create campaign data
            campaign_data = {
                "name": request.name,
                "description": request.description,
                "template_id": request.template_id,
                "status": CampaignStatus.DRAFT,
                "audience": request.audience.model_dump(),
                "schedule": request.schedule.model_dump(),
                "ab_test": request.ab_test.model_dump(),
                "total_recipients": 0,
                "emails_sent": 0,
                "emails_delivered": 0,
                "emails_opened": 0,
                "emails_clicked": 0,
                "emails_bounced": 0,
                "unsubscribes": 0,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "created_by": created_by
            }

            # Insert into database
            db = self._ensure_db()
            result = await db.email_campaigns.insert_one(campaign_data)
            campaign_data["_id"] = str(result.inserted_id)

            logger.info(f"Created email campaign: {request.name}")
            return EmailCampaign(**campaign_data)

        except Exception as e:
            logger.error(f"Error creating email campaign: {str(e)}")
            raise

    async def get_campaign(self, campaign_id: str) -> Optional[EmailCampaign]:
        """Get email campaign by ID."""
        try:
            db = self._ensure_db()
            campaign_data = await db.email_campaigns.find_one(
                {"_id": ObjectId(campaign_id)}
            )

            if not campaign_data:
                return None

            campaign_data["_id"] = str(campaign_data["_id"])
            return EmailCampaign(**campaign_data)

        except Exception as e:
            logger.error(f"Error getting email campaign {campaign_id}: {str(e)}")
            return None

    async def list_campaigns(
        self,
        request: CampaignListRequest
    ) -> Tuple[List[EmailCampaign], int]:
        """List email campaigns with filtering and pagination."""
        try:
            # Build query filter
            query_filter = {}

            if request.status:
                query_filter["status"] = request.status

            if request.template_id:
                query_filter["template_id"] = request.template_id

            if request.created_by:
                query_filter["created_by"] = request.created_by

            if request.search:
                query_filter["$or"] = [
                    {"name": {"$regex": request.search, "$options": "i"}},
                    {"description": {"$regex": request.search, "$options": "i"}}
                ]

            # Get total count
            db = self._ensure_db()
            total = await db.email_campaigns.count_documents(query_filter)

            # Get campaigns with pagination
            cursor = db.email_campaigns.find(query_filter).sort("created_at", -1)
            cursor = cursor.skip(request.skip).limit(request.limit)

            campaigns = []
            async for campaign_data in cursor:
                campaign_data["_id"] = str(campaign_data["_id"])
                campaigns.append(EmailCampaign(**campaign_data))

            return campaigns, total

        except Exception as e:
            logger.error(f"Error listing email campaigns: {str(e)}")
            return [], 0

    # Trigger Management Methods

    async def create_trigger(
        self,
        request: TriggerCreateRequest,
        created_by: str
    ) -> EmailTrigger:
        """Create a new email trigger."""
        try:
            # Validate template exists
            template = await self.get_template(request.template_id)
            if not template:
                raise ValueError(f"Template {request.template_id} not found")

            # Create trigger data
            trigger_data = {
                "name": request.name,
                "description": request.description,
                "trigger_type": request.trigger_type,
                "event_name": request.event_name,
                "template_id": request.template_id,
                "conditions": request.conditions,
                "delay_minutes": request.delay_minutes,
                "is_active": True,
                "max_sends_per_user": request.max_sends_per_user,
                "cooldown_hours": request.cooldown_hours,
                "trigger_count": 0,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "created_by": created_by
            }

            # Insert into database
            db = self._ensure_db()
            result = await db.email_triggers.insert_one(trigger_data)
            trigger_data["_id"] = str(result.inserted_id)

            logger.info(f"Created email trigger: {request.name}")
            return EmailTrigger(**trigger_data)

        except Exception as e:
            logger.error(f"Error creating email trigger: {str(e)}")
            raise

    async def render_template(
        self,
        template_id: str,
        template_data: Dict[str, Any]
    ) -> Dict[str, str | None]:
        """Render template with provided data."""
        try:
            template = await self.get_template(template_id)
            if not template:
                raise ValueError(f"Template {template_id} not found")

            # Render HTML content
            html_template = self.jinja_env.from_string(template.html_content)
            rendered_html = html_template.render(**template_data)

            # Render subject
            subject_template = self.jinja_env.from_string(template.subject)
            rendered_subject = subject_template.render(**template_data)

            # Render text content if available
            rendered_text = None
            if template.text_content:
                text_template = self.jinja_env.from_string(template.text_content)
                rendered_text = text_template.render(**template_data)

            return {
                "html": rendered_html or "",
                "text": rendered_text,
                "subject": rendered_subject or ""
            }

        except Exception as e:
            logger.error(f"Error rendering template {template_id}: {str(e)}")
            raise


# Global service instance
email_template_enhanced_service = EmailTemplateEnhancedService()
