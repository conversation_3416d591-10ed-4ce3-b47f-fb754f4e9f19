"""
Knowledge Base Management API Routes

This module provides comprehensive REST API endpoints for knowledge base
management in the ACE Social admin panel.

@since 2024-1-1 to 2025-25-7
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from fastapi import status as http_status
from fastapi.security import HTT<PERSON><PERSON>earer

from app.middleware.auth import get_current_admin_user, get_current_user
from app.services.advanced_rate_limiter import rate_limit
from app.models.user import User
from app.models.knowledge_base import (
    KnowledgeBaseArticle, ArticleTemplate, ArticleStatus,
    ArticleType, KnowledgeBaseCategory, DifficultyLevel
)
from app.schemas.knowledge_base import (
    ArticleCreateRequest, ArticleUpdateRequest, ArticleListRequest,
    ArticleResponse, ArticleListResponse, ArticleDuplicateRequest,
    ArticleSearchRequest, ArticleSearchResponse, ArticleSearchResult, ArticleAnalyticsRequest,
    ArticleAnalyticsResponse, TemplateCreateRequest, TemplateUpdateRequest,
    TemplateResponse, TemplateListResponse, KnowledgeBaseDashboardResponse,
    BulkOperationRequest, BulkOperationResponse, CommentCreateRequest,
    CommentResponse, EmailIntegrationRequest, EmailIntegrationResponse,
    KnowledgeBaseSettingsRequest, KnowledgeBaseSettingsResponse
)
from app.services.knowledge_base_service import knowledge_base_service
from app.utils.response import create_response, create_error_response
import logging
from datetime import datetime, timezone, timedelta

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/admin/knowledge-base", tags=["knowledge-base"])
public_router = APIRouter(prefix="/api/knowledge-base", tags=["knowledge-base-public"])
security = HTTPBearer()


# Admin Article Management Endpoints

@router.post("/articles", response_model=ArticleResponse)
@rate_limit(calls=20, period=60)  # 20 creates per minute
async def create_article(
    request: Request,
    article_request: ArticleCreateRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """Create a new knowledge base article."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()
        
        # Create the article
        article = await knowledge_base_service.create_article(
            article_request,
            str(current_admin.id),
            current_admin.full_name or current_admin.email
        )
        
        # Convert to response format
        response_data = article.dict()
        response_data["id"] = str(response_data.pop("_id"))
        response_data["author_id"] = str(response_data["author_id"])
        response_data["related_articles"] = [str(id) for id in response_data.get("related_articles", [])]
        
        return create_response(
            data=ArticleResponse(**response_data),
            message="Knowledge base article created successfully"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating knowledge base article: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create knowledge base article: {str(e)}"
        )


@router.get("/articles", response_model=ArticleListResponse)
@rate_limit(calls=100, period=60)  # 100 requests per minute
async def list_articles(
    request: Request,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    category: Optional[str] = Query(None),
    article_type: Optional[str] = Query(None),
    article_status: Optional[str] = Query(None),
    difficulty_level: Optional[str] = Query(None),
    is_featured: Optional[bool] = Query(None),
    is_internal: Optional[bool] = Query(None),
    author_id: Optional[str] = Query(None),
    tags: Optional[List[str]] = Query(None),
    search: Optional[str] = Query(None),
    sort_by: str = Query("updated_at"),
    sort_order: str = Query("desc"),
    include_drafts: bool = Query(True),
    include_archived: bool = Query(False),
    current_admin: User = Depends(get_current_admin_user)
):
    """List knowledge base articles with filtering."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()
        
        # Create list request
        list_request = ArticleListRequest(
            skip=skip,
            limit=limit,
            category=KnowledgeBaseCategory(category) if category else None,
            article_type=ArticleType(article_type) if article_type else None,
            status=ArticleStatus(article_status) if article_status else None,
            difficulty_level=DifficultyLevel(difficulty_level) if difficulty_level else None,
            is_featured=is_featured,
            is_internal=is_internal,
            author_id=author_id,
            tags=tags,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order,
            include_drafts=include_drafts,
            include_archived=include_archived
        )
        
        # Get articles
        articles, total = await knowledge_base_service.list_articles(list_request)
        
        # Convert to response format
        article_responses = []
        for article in articles:
            response_data = article.dict()
            response_data["id"] = str(response_data.pop("_id"))
            response_data["author_id"] = str(response_data["author_id"])
            response_data["related_articles"] = [str(id) for id in response_data.get("related_articles", [])]
            if response_data.get("reviewer_id"):
                response_data["reviewer_id"] = str(response_data["reviewer_id"])
            article_responses.append(ArticleResponse(**response_data))
        
        return create_response(
            data=ArticleListResponse(
                articles=article_responses,
                total=total,
                skip=skip,
                limit=limit,
                has_more=skip + limit < total
            ),
            message=f"Retrieved {len(article_responses)} knowledge base articles"
        )
        
    except Exception as e:
        logger.error(f"Error listing knowledge base articles: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list knowledge base articles: {str(e)}"
        )


@router.get("/articles/{article_id}", response_model=ArticleResponse)
@rate_limit(calls=100, period=60)
async def get_article(
    request: Request,
    article_id: str,
    current_admin: User = Depends(get_current_admin_user)
):
    """Get knowledge base article by ID."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()
        
        # Get article
        article = await knowledge_base_service.get_article(article_id)
        if not article:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Knowledge base article not found"
            )
        
        # Convert to response format
        response_data = article.dict()
        response_data["id"] = str(response_data.pop("_id"))
        response_data["author_id"] = str(response_data["author_id"])
        response_data["related_articles"] = [str(id) for id in response_data.get("related_articles", [])]
        if response_data.get("reviewer_id"):
            response_data["reviewer_id"] = str(response_data["reviewer_id"])
        
        return create_response(
            data=ArticleResponse(**response_data),
            message="Knowledge base article retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting knowledge base article {article_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get knowledge base article: {str(e)}"
        )


@router.put("/articles/{article_id}", response_model=ArticleResponse)
@rate_limit(calls=30, period=60)  # 30 updates per minute
async def update_article(
    request: Request,
    article_id: str,
    article_request: ArticleUpdateRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """Update knowledge base article."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()
        
        # Update the article
        article = await knowledge_base_service.update_article(
            article_id,
            article_request,
            str(current_admin.id),
            current_admin.full_name or current_admin.email
        )
        
        if not article:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Knowledge base article not found"
            )
        
        # Convert to response format
        response_data = article.dict()
        response_data["id"] = str(response_data.pop("_id"))
        response_data["author_id"] = str(response_data["author_id"])
        response_data["related_articles"] = [str(id) for id in response_data.get("related_articles", [])]
        if response_data.get("reviewer_id"):
            response_data["reviewer_id"] = str(response_data["reviewer_id"])
        
        return create_response(
            data=ArticleResponse(**response_data),
            message="Knowledge base article updated successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating knowledge base article {article_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update knowledge base article: {str(e)}"
        )


@router.delete("/articles/{article_id}")
@rate_limit(calls=10, period=60)  # 10 deletes per minute
async def delete_article(
    request: Request,
    article_id: str,
    current_admin: User = Depends(get_current_admin_user)
):
    """Delete knowledge base article."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()
        
        # Delete the article
        success = await knowledge_base_service.delete_article(article_id)
        
        if not success:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Knowledge base article not found"
            )
        
        return create_response(
            data={"deleted": True},
            message="Knowledge base article deleted successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting knowledge base article {article_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete knowledge base article: {str(e)}"
        )


@router.post("/articles/{article_id}/duplicate", response_model=ArticleResponse)
@rate_limit(calls=10, period=60)  # 10 duplicates per minute
async def duplicate_article(
    request: Request,
    article_id: str,
    duplicate_request: ArticleDuplicateRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """Duplicate an existing knowledge base article."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()
        
        # Get original article
        original_article = await knowledge_base_service.get_article(article_id)
        if not original_article:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Knowledge base article not found"
            )
        
        # Create duplicate request
        create_request = ArticleCreateRequest(
            title=duplicate_request.new_title,
            content=original_article.content if duplicate_request.copy_content else "",
            summary=duplicate_request.new_summary or original_article.summary,
            content_format=original_article.content_format,
            category=original_article.category,
            article_type=original_article.article_type,
            tags=original_article.tags if duplicate_request.copy_tags else [],
            difficulty_level=original_article.difficulty_level,
            status=ArticleStatus.DRAFT,  # Always create duplicates as drafts
            is_featured=False,
            is_internal=original_article.is_internal,
            seo=original_article.seo if duplicate_request.copy_seo else None,
            search_keywords=original_article.search_keywords if duplicate_request.copy_tags else [],
            email_template_variables=original_article.email_template_variables,
            can_embed_in_emails=original_article.can_embed_in_emails
        )
        
        # Create the duplicate
        duplicate_article = await knowledge_base_service.create_article(
            create_request,
            str(current_admin.id),
            current_admin.full_name or current_admin.email
        )
        
        # Convert to response format
        response_data = duplicate_article.dict()
        response_data["id"] = str(response_data.pop("_id"))
        response_data["author_id"] = str(response_data["author_id"])
        response_data["related_articles"] = [str(id) for id in response_data.get("related_articles", [])]
        
        return create_response(
            data=ArticleResponse(**response_data),
            message="Knowledge base article duplicated successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error duplicating knowledge base article {article_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to duplicate knowledge base article: {str(e)}"
        )


# Search and Analytics Endpoints

@router.get("/search", response_model=ArticleSearchResponse)
@rate_limit(calls=60, period=60)  # 60 searches per minute
async def search_articles(
    request: Request,
    query: str = Query(..., min_length=1, max_length=200),
    category: Optional[str] = Query(None),
    article_type: Optional[str] = Query(None),
    difficulty_level: Optional[str] = Query(None),
    tags: Optional[List[str]] = Query(None),
    include_internal: bool = Query(False),
    limit: int = Query(10, ge=1, le=50),
    include_content: bool = Query(False),
    current_admin: User = Depends(get_current_admin_user)
):
    """Search knowledge base articles."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()

        # Create search request
        search_request = ArticleSearchRequest(
            query=query,
            category=KnowledgeBaseCategory(category) if category else None,
            article_type=ArticleType(article_type) if article_type else None,
            difficulty_level=DifficultyLevel(difficulty_level) if difficulty_level else None,
            tags=tags,
            include_internal=include_internal,
            limit=limit,
            include_content=include_content
        )

        # Perform search
        results, total_results, search_time = await knowledge_base_service.search_articles(
            search_request,
            str(current_admin.id)
        )

        # Convert results to ArticleSearchResult objects
        search_results = []
        for result in results:
            search_result = ArticleSearchResult(
                id=str(result.get("_id", result.get("id", ""))),
                title=result.get("title", ""),
                summary=result.get("summary"),
                category=result.get("category", KnowledgeBaseCategory.GENERAL),
                article_type=result.get("article_type", ArticleType.HELP_ARTICLE),
                tags=result.get("tags", []),
                difficulty_level=result.get("difficulty_level", DifficultyLevel.BEGINNER),
                view_count=result.get("view_count", 0),
                helpfulness_score=result.get("helpfulness_score", 0.0),
                reading_time_minutes=result.get("reading_time_minutes", 0),
                relevance_score=result.get("relevance_score", 0.0),
                highlighted_content=result.get("highlighted_content"),
                created_at=result.get("created_at", datetime.now(timezone.utc)),
                updated_at=result.get("updated_at", datetime.now(timezone.utc))
            )
            search_results.append(search_result)

        return create_response(
            data=ArticleSearchResponse(
                results=search_results,
                total_results=total_results,
                query=query,
                search_time_ms=search_time,
                suggestions=[]  # Could be implemented with search suggestions
            ),
            message=f"Found {total_results} articles matching '{query}'"
        )

    except Exception as e:
        logger.error(f"Error searching knowledge base articles: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search knowledge base articles: {str(e)}"
        )


@router.get("/articles/{article_id}/analytics", response_model=ArticleAnalyticsResponse)
@rate_limit(calls=30, period=60)
async def get_article_analytics(
    request: Request,
    article_id: str,
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    current_admin: User = Depends(get_current_admin_user)
):
    """Get analytics for a specific article."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()

        # Parse dates if provided
        start_dt = None
        end_dt = None
        if start_date:
            from datetime import datetime
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        if end_date:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))

        # Get analytics
        default_start = datetime.now(timezone.utc) - timedelta(days=30)
        default_end = datetime.now(timezone.utc)

        analytics = await knowledge_base_service.get_article_analytics(
            article_id,
            start_dt or default_start,
            end_dt or default_end
        )

        if not analytics:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Article not found or no analytics data available"
            )

        return create_response(
            data=analytics,
            message="Article analytics retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting article analytics {article_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get article analytics: {str(e)}"
        )


@router.get("/dashboard", response_model=KnowledgeBaseDashboardResponse)
@rate_limit(calls=60, period=60)  # 60 requests per minute
async def get_knowledge_base_dashboard(
    request: Request,
    current_admin: User = Depends(get_current_admin_user)
):
    """Get knowledge base dashboard overview."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()

        # Get dashboard data
        dashboard_data = await knowledge_base_service.get_dashboard_data()

        return create_response(
            data=dashboard_data,
            message="Knowledge base dashboard retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error getting knowledge base dashboard: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get knowledge base dashboard: {str(e)}"
        )


# Template Management Endpoints

@router.post("/templates", response_model=TemplateResponse)
@rate_limit(calls=10, period=60)  # 10 creates per minute
async def create_template(
    request: Request,
    template_request: TemplateCreateRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """Create a new article template."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()

        # Create the template
        template = await knowledge_base_service.create_template(
            template_request,
            str(current_admin.id)
        )

        # Convert to response format
        response_data = template.dict()
        response_data["id"] = str(response_data.pop("_id"))
        response_data["created_by"] = str(response_data["created_by"])

        return create_response(
            data=TemplateResponse(**response_data),
            message="Article template created successfully"
        )

    except Exception as e:
        logger.error(f"Error creating article template: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create article template: {str(e)}"
        )


@router.get("/templates", response_model=TemplateListResponse)
@rate_limit(calls=100, period=60)
async def list_templates(
    request: Request,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    article_type: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None),
    current_admin: User = Depends(get_current_admin_user)
):
    """List article templates with filtering."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()

        # Create filters
        filters = {
            'skip': skip,
            'limit': limit,
            'article_type': article_type,
            'category': category,
            'is_active': is_active
        }

        # Get templates
        templates, total = await knowledge_base_service.list_templates(filters)

        # Convert to response format
        template_responses = []
        for template in templates:
            response_data = template.dict()
            response_data["id"] = str(response_data.pop("_id"))
            response_data["created_by"] = str(response_data["created_by"])
            template_responses.append(TemplateResponse(**response_data))

        return create_response(
            data=TemplateListResponse(
                templates=template_responses,
                total=total
            ),
            message=f"Retrieved {len(template_responses)} article templates"
        )

    except Exception as e:
        logger.error(f"Error listing article templates: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list article templates: {str(e)}"
        )


@router.get("/templates/{template_id}", response_model=TemplateResponse)
@rate_limit(calls=100, period=60)
async def get_template(
    request: Request,
    template_id: str,
    current_admin: User = Depends(get_current_admin_user)
):
    """Get article template by ID."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()

        # Get template
        template = await knowledge_base_service.get_template(template_id)
        if not template:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Article template not found"
            )

        # Convert to response format
        response_data = template.dict()
        response_data["id"] = str(response_data.pop("_id"))
        response_data["created_by"] = str(response_data["created_by"])

        return create_response(
            data=TemplateResponse(**response_data),
            message="Article template retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting article template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get article template: {str(e)}"
        )


@router.put("/templates/{template_id}", response_model=TemplateResponse)
@rate_limit(calls=30, period=60)
async def update_template(
    request: Request,
    template_id: str,
    template_request: TemplateUpdateRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """Update article template."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()

        # Update the template
        template = await knowledge_base_service.update_template(template_id, template_request)

        if not template:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Article template not found"
            )

        # Convert to response format
        response_data = template.dict()
        response_data["id"] = str(response_data.pop("_id"))
        response_data["created_by"] = str(response_data["created_by"])

        return create_response(
            data=TemplateResponse(**response_data),
            message="Article template updated successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating article template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update article template: {str(e)}"
        )


@router.delete("/templates/{template_id}")
@rate_limit(calls=10, period=60)
async def delete_template(
    request: Request,
    template_id: str,
    current_admin: User = Depends(get_current_admin_user)
):
    """Delete article template."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()

        # Delete the template
        success = await knowledge_base_service.delete_template(template_id)

        if not success:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Article template not found"
            )

        return create_response(
            data={"deleted": True},
            message="Article template deleted successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting article template {template_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete article template: {str(e)}"
        )


# Bulk Operations

@router.post("/articles/bulk", response_model=BulkOperationResponse)
@rate_limit(calls=5, period=60)  # 5 bulk operations per minute
async def bulk_article_operation(
    request: Request,
    bulk_request: BulkOperationRequest,
    current_admin: User = Depends(get_current_admin_user)
):
    """Perform bulk operations on knowledge base articles."""
    try:
        if not bulk_request.confirm:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="Bulk operation must be confirmed"
            )

        # Ensure service is initialized
        await knowledge_base_service.initialize()

        successful_items = 0
        failed_items = 0
        errors = []
        processed_ids = []

        for article_id in bulk_request.article_ids:
            try:
                if bulk_request.operation == "publish":
                    # Update status to published
                    update_request = ArticleUpdateRequest(
                        status=ArticleStatus.PUBLISHED,
                        change_summary=f"Bulk publish operation by {current_admin.full_name or current_admin.email}",
                        title=None,
                        content=None,
                        summary=None
                    )
                    result = await knowledge_base_service.update_article(
                        article_id, update_request, str(current_admin.id),
                        current_admin.full_name or current_admin.email
                    )
                    if result:
                        successful_items += 1
                        processed_ids.append(article_id)
                    else:
                        failed_items += 1
                        errors.append({"id": article_id, "error": "Article not found"})

                elif bulk_request.operation == "archive":
                    update_request = ArticleUpdateRequest(
                        status=ArticleStatus.ARCHIVED,
                        change_summary=f"Bulk archive operation by {current_admin.full_name or current_admin.email}",
                        title=None,
                        content=None,
                        summary=None
                    )
                    result = await knowledge_base_service.update_article(
                        article_id, update_request, str(current_admin.id),
                        current_admin.full_name or current_admin.email
                    )
                    if result:
                        successful_items += 1
                        processed_ids.append(article_id)
                    else:
                        failed_items += 1
                        errors.append({"id": article_id, "error": "Article not found"})

                elif bulk_request.operation == "delete":
                    success = await knowledge_base_service.delete_article(article_id)
                    if success:
                        successful_items += 1
                        processed_ids.append(article_id)
                    else:
                        failed_items += 1
                        errors.append({"id": article_id, "error": "Article not found"})

                elif bulk_request.operation == "update_category":
                    new_category = bulk_request.parameters.get("category")
                    if not new_category:
                        failed_items += 1
                        errors.append({"id": article_id, "error": "Category parameter required"})
                        continue

                    update_request = ArticleUpdateRequest(
                        category=new_category,
                        change_summary=f"Bulk category update to {new_category}",
                        title=None,
                        content=None,
                        summary=None
                    )
                    result = await knowledge_base_service.update_article(
                        article_id, update_request, str(current_admin.id),
                        current_admin.full_name or current_admin.email
                    )
                    if result:
                        successful_items += 1
                        processed_ids.append(article_id)
                    else:
                        failed_items += 1
                        errors.append({"id": article_id, "error": "Article not found"})

            except Exception as e:
                failed_items += 1
                errors.append({"id": article_id, "error": str(e)})

        return create_response(
            data=BulkOperationResponse(
                operation=bulk_request.operation,
                total_items=len(bulk_request.article_ids),
                successful_items=successful_items,
                failed_items=failed_items,
                errors=errors,
                processed_ids=processed_ids
            ),
            message=f"Bulk {bulk_request.operation} operation completed"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error performing bulk operation: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to perform bulk operation: {str(e)}"
        )


# Public Knowledge Base Endpoints (Customer Portal)

@public_router.get("/articles", response_model=ArticleListResponse)
@rate_limit(calls=200, period=60)  # Higher limit for public access
async def public_list_articles(
    request: Request,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=50),
    category: Optional[str] = Query(None),
    article_type: Optional[str] = Query(None),
    difficulty_level: Optional[str] = Query(None),
    tags: Optional[List[str]] = Query(None),
    search: Optional[str] = Query(None),
    sort_by: str = Query("updated_at"),
    sort_order: str = Query("desc"),
    current_user: Optional[User] = Depends(get_current_user)
):
    """Public endpoint to list published knowledge base articles."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()

        # Create list request (only published, non-internal articles)
        list_request = ArticleListRequest(
            skip=skip,
            limit=limit,
            category=KnowledgeBaseCategory(category) if category else None,
            article_type=ArticleType(article_type) if article_type else None,
            status=ArticleStatus.PUBLISHED,  # Only published articles
            difficulty_level=DifficultyLevel(difficulty_level) if difficulty_level else None,
            is_internal=False,  # Only public articles
            tags=tags,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order,
            include_drafts=False,
            include_archived=False
        )

        # Get articles
        articles, total = await knowledge_base_service.list_articles(list_request)

        # Convert to response format (limited fields for public)
        article_responses = []
        for article in articles:
            response_data = {
                "id": str(article.id),
                "title": article.title,
                "summary": article.summary,
                "category": article.category,
                "article_type": article.article_type,
                "tags": article.tags,
                "difficulty_level": article.difficulty_level,
                "view_count": article.view_count,
                "helpful_votes": article.helpful_votes,
                "not_helpful_votes": article.not_helpful_votes,
                "helpfulness_score": article.helpfulness_score,
                "reading_time_minutes": article.reading_time_minutes,
                "created_at": article.created_at,
                "updated_at": article.updated_at,
                "featured_image": article.featured_image
            }
            article_responses.append(response_data)

        return create_response(
            data={
                "articles": article_responses,
                "total": total,
                "skip": skip,
                "limit": limit,
                "has_more": skip + limit < total
            },
            message=f"Retrieved {len(article_responses)} knowledge base articles"
        )

    except Exception as e:
        logger.error(f"Error listing public knowledge base articles: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list knowledge base articles: {str(e)}"
        )


@public_router.get("/articles/{article_id}")
@rate_limit(calls=200, period=60)
async def public_get_article(
    request: Request,
    article_id: str,
    current_user: Optional[User] = Depends(get_current_user)
):
    """Public endpoint to get a published knowledge base article."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()

        # Get article
        article = await knowledge_base_service.get_article(article_id)
        if not article:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Knowledge base article not found"
            )

        # Check if article is published and public
        if article.status != "published" or article.is_internal:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Knowledge base article not found"
            )

        # Increment view count
        if knowledge_base_service.db:
            await knowledge_base_service.db.knowledge_base_articles.update_one(
                {"_id": article.id},
                {"$inc": {"view_count": 1}}
            )

        # Track analytics if user is logged in
        if current_user:
            # Could track detailed analytics here
            pass

        # Return public article data
        response_data = {
            "id": str(article.id),
            "title": article.title,
            "content": article.content,
            "summary": article.summary,
            "content_format": article.content_format,
            "category": article.category,
            "article_type": article.article_type,
            "tags": article.tags,
            "difficulty_level": article.difficulty_level,
            "view_count": article.view_count + 1,  # Include the increment
            "helpful_votes": article.helpful_votes,
            "not_helpful_votes": article.not_helpful_votes,
            "helpfulness_score": article.helpfulness_score,
            "reading_time_minutes": article.reading_time_minutes,
            "author_name": article.author_name,
            "created_at": article.created_at,
            "updated_at": article.updated_at,
            "published_at": article.published_at,
            "featured_image": article.featured_image,
            "attachments": article.attachments
        }

        return create_response(
            data=response_data,
            message="Knowledge base article retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting public knowledge base article {article_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get knowledge base article: {str(e)}"
        )


@public_router.get("/search")
@rate_limit(calls=100, period=60)
async def public_search_articles(
    request: Request,
    query: str = Query(..., min_length=1, max_length=200),
    category: Optional[str] = Query(None),
    article_type: Optional[str] = Query(None),
    difficulty_level: Optional[str] = Query(None),
    tags: Optional[List[str]] = Query(None),
    limit: int = Query(10, ge=1, le=20),
    current_user: Optional[User] = Depends(get_current_user)
):
    """Public endpoint to search published knowledge base articles."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()

        # Create search request (only public articles)
        search_request = ArticleSearchRequest(
            query=query,
            category=KnowledgeBaseCategory(category) if category else None,
            article_type=ArticleType(article_type) if article_type else None,
            difficulty_level=DifficultyLevel(difficulty_level) if difficulty_level else None,
            tags=tags,
            include_internal=False,  # Only public articles
            limit=limit,
            include_content=False
        )

        # Perform search
        user_id = str(current_user.id) if current_user else "anonymous"
        results, total_results, search_time = await knowledge_base_service.search_articles(
            search_request,
            user_id
        )

        return create_response(
            data={
                "results": results,
                "total_results": total_results,
                "query": query,
                "search_time_ms": search_time
            },
            message=f"Found {total_results} articles matching '{query}'"
        )

    except Exception as e:
        logger.error(f"Error searching public knowledge base articles: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search knowledge base articles: {str(e)}"
        )


@public_router.post("/articles/{article_id}/vote")
@rate_limit(calls=10, period=60)
async def vote_on_article(
    request: Request,
    article_id: str,
    is_helpful: bool,
    current_user: Optional[User] = Depends(get_current_user)
):
    """Vote on article helpfulness."""
    try:
        # Ensure service is initialized
        await knowledge_base_service.initialize()

        # Get article
        article = await knowledge_base_service.get_article(article_id)
        if not article or article.status != "published" or article.is_internal:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Knowledge base article not found"
            )

        # Update vote count
        update_field = "helpful_votes" if is_helpful else "not_helpful_votes"
        if knowledge_base_service.db:
            await knowledge_base_service.db.knowledge_base_articles.update_one(
                {"_id": article.id},
                {"$inc": {update_field: 1}}
            )

        return create_response(
            data={"voted": True, "is_helpful": is_helpful},
            message="Vote recorded successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error voting on article {article_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to record vote: {str(e)}"
        )
