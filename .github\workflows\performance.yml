name: Performance Testing

on:
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to test'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      duration:
        description: 'Test duration (minutes)'
        required: true
        default: '10'
        type: string

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  # ================================
  # Bundle Size Analysis
  # ================================
  bundle-analysis:
    name: Bundle Size Analysis
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'package-lock.json'

    - name: Install dependencies
      run: npm run install:all

    - name: Build for analysis
      run: npm run build:analyze

    - name: Analyze bundle size
      run: |
        cd frontend
        npx bundlesize
        npx webpack-bundle-analyzer dist/assets/*.js --report --mode static --report-filename ../bundle-report.html

    - name: Upload bundle analysis
      uses: actions/upload-artifact@v4
      with:
        name: bundle-analysis
        path: |
          frontend/bundle-report.html
          frontend/dist/
        retention-days: 30

    - name: Comment bundle size
      uses: actions/github-script@v7
      if: github.event_name == 'pull_request'
      with:
        script: |
          const fs = require('fs');
          const path = require('path');
          
          // Read bundle sizes
          const distPath = 'frontend/dist';
          const files = fs.readdirSync(distPath, { recursive: true });
          
          let totalSize = 0;
          let jsSize = 0;
          let cssSize = 0;
          
          files.forEach(file => {
            const filePath = path.join(distPath, file);
            const stats = fs.statSync(filePath);
            if (stats.isFile()) {
              totalSize += stats.size;
              if (file.endsWith('.js')) jsSize += stats.size;
              if (file.endsWith('.css')) cssSize += stats.size;
            }
          });
          
          const formatBytes = (bytes) => {
            return (bytes / 1024 / 1024).toFixed(2) + ' MB';
          };
          
          const comment = `## 📊 Bundle Size Analysis
          
          | Asset Type | Size |
          |------------|------|
          | JavaScript | ${formatBytes(jsSize)} |
          | CSS | ${formatBytes(cssSize)} |
          | **Total** | **${formatBytes(totalSize)}** |
          
          ${totalSize > 2 * 1024 * 1024 ? '⚠️ Bundle size exceeds 2MB target' : '✅ Bundle size within target'}
          `;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  # ================================
  # Lighthouse Performance Audit
  # ================================
  lighthouse-audit:
    name: Lighthouse Performance Audit
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm ci
        cd frontend && npm ci

    - name: Build application
      run: |
        cd frontend && npm run build:production

    - name: Start application
      run: |
        cd frontend && npm run preview &
        sleep 10

    - name: Run Lighthouse audit
      run: |
        npm install -g @lhci/cli
        lhci autorun
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN || '' }}

    - name: Upload Lighthouse results
      uses: actions/upload-artifact@v4
      if: always() && hashFiles('.lighthouseci/**/*') != ''
      with:
        name: lighthouse-results
        path: .lighthouseci/
        retention-days: 30

  # ================================
  # Load Testing
  # ================================
  load-testing:
    name: Load Testing
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Install k6
      run: |
        sudo gpg -k
        sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
        echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
        sudo apt-get update
        sudo apt-get install k6

    - name: Create load test script
      run: |
        cat > load-test.js << 'EOF'
        import http from 'k6/http';
        import { check, sleep } from 'k6';
        import { Rate } from 'k6/metrics';

        const errorRate = new Rate('errors');
        const BASE_URL = __ENV.BASE_URL || 'https://staging.acesocial.com';

        export let options = {
          stages: [
            { duration: '2m', target: 10 },   // Ramp up
            { duration: '5m', target: 50 },   // Stay at 50 users
            { duration: '2m', target: 100 },  // Ramp up to 100 users
            { duration: '5m', target: 100 },  // Stay at 100 users
            { duration: '2m', target: 0 },    // Ramp down
          ],
          thresholds: {
            http_req_duration: ['p(95)<500'], // 95% of requests under 500ms
            http_req_failed: ['rate<0.05'],   // Error rate under 5%
            errors: ['rate<0.05'],
          },
        };

        export default function() {
          // Test homepage
          let response = http.get(`${BASE_URL}/`);
          check(response, {
            'homepage status is 200': (r) => r.status === 200,
            'homepage loads in <500ms': (r) => r.timings.duration < 500,
          }) || errorRate.add(1);

          sleep(1);

          // Test API health endpoint
          response = http.get(`${BASE_URL}/api/health`);
          check(response, {
            'health endpoint status is 200': (r) => r.status === 200,
            'health endpoint responds in <200ms': (r) => r.timings.duration < 200,
          }) || errorRate.add(1);

          sleep(1);
        }
        EOF

    - name: Run load test
      run: |
        k6 run --duration ${{ github.event.inputs.duration }}m load-test.js
      env:
        BASE_URL: ${{ github.event.inputs.environment == 'production' && 'https://acesocial.com' || 'https://staging.acesocial.com' }}

    - name: Upload load test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: load-test-results
        path: |
          *.json
          *.html
        retention-days: 30

  # ================================
  # Database Performance Testing
  # ================================
  database-performance:
    name: Database Performance Testing
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:7.0
        env:
          MONGO_INITDB_ROOT_USERNAME: admin
          MONGO_INITDB_ROOT_PASSWORD: password123
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongosh --eval 'db.adminCommand(\"ping\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7.2-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        cd backend
        pip install -r requirements.txt
        pip install pymongo redis pytest-benchmark

    - name: Create performance test script
      run: |
        cat > backend/test_performance.py << 'EOF'
        import pytest
        import time
        import random
        from pymongo import MongoClient
        import redis
        from app.database import get_database
        from app.cache import get_redis_client

        class TestDatabasePerformance:
            def setup_method(self):
                self.db = get_database()
                self.redis_client = get_redis_client()
                
            def test_mongodb_insert_performance(self, benchmark):
                def insert_document():
                    doc = {
                        "user_id": f"user_{random.randint(1, 10000)}",
                        "content": f"Test content {random.randint(1, 1000)}",
                        "timestamp": time.time(),
                        "metadata": {"test": True}
                    }
                    return self.db.posts.insert_one(doc)
                
                result = benchmark(insert_document)
                assert result.inserted_id is not None

            def test_mongodb_query_performance(self, benchmark):
                # Insert test data first
                test_docs = []
                for i in range(100):
                    test_docs.append({
                        "user_id": f"test_user_{i}",
                        "content": f"Test content {i}",
                        "timestamp": time.time() + i
                    })
                self.db.posts.insert_many(test_docs)
                
                def query_documents():
                    return list(self.db.posts.find({"user_id": {"$regex": "test_user_"}}).limit(10))
                
                result = benchmark(query_documents)
                assert len(result) == 10

            def test_redis_set_performance(self, benchmark):
                def set_cache():
                    key = f"test_key_{random.randint(1, 10000)}"
                    value = f"test_value_{random.randint(1, 1000)}"
                    return self.redis_client.set(key, value, ex=3600)
                
                result = benchmark(set_cache)
                assert result is True

            def test_redis_get_performance(self, benchmark):
                # Set test data first
                test_keys = []
                for i in range(100):
                    key = f"perf_test_key_{i}"
                    self.redis_client.set(key, f"value_{i}", ex=3600)
                    test_keys.append(key)
                
                def get_cache():
                    key = random.choice(test_keys)
                    return self.redis_client.get(key)
                
                result = benchmark(get_cache)
                assert result is not None
        EOF

    - name: Run database performance tests
      run: |
        cd backend
        python -m pytest test_performance.py --benchmark-json=performance_results.json -v
      env:
        MONGODB_URL: *********************************************************************************
        REDIS_URL: redis://localhost:6379/2

    - name: Upload performance results
      uses: actions/upload-artifact@v4
      with:
        name: database-performance-results
        path: backend/performance_results.json
        retention-days: 30

  # ================================
  # Performance Report
  # ================================
  performance-report:
    name: Generate Performance Report
    runs-on: ubuntu-latest
    needs: [bundle-analysis, lighthouse-audit, load-testing, database-performance]
    if: always()
    
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v4
      with:
        merge-multiple: true

    - name: Generate performance report
      run: |
        cat > performance-report.md << 'EOF'
        # 📊 Performance Test Report
        
        **Date:** $(date)
        **Environment:** ${{ github.event.inputs.environment || 'staging' }}
        **Duration:** ${{ github.event.inputs.duration || '10' }} minutes
        
        ## Bundle Size Analysis
        - Frontend bundle analyzed
        - Size optimization recommendations provided
        
        ## Lighthouse Audit
        - Performance score measured
        - Core Web Vitals assessed
        - Accessibility and SEO scores included
        
        ## Load Testing
        - Concurrent user simulation completed
        - Response time thresholds validated
        - Error rate monitoring performed
        
        ## Database Performance
        - MongoDB query performance tested
        - Redis cache performance validated
        - Benchmark results recorded
        
        ## Recommendations
        1. Monitor bundle size growth
        2. Optimize images and assets
        3. Implement caching strategies
        4. Database query optimization
        5. CDN configuration review
        
        ## Next Steps
        - Review detailed results in artifacts
        - Address performance bottlenecks
        - Schedule regular performance testing
        EOF

    - name: Upload performance report
      uses: actions/upload-artifact@v4
      with:
        name: performance-report
        path: performance-report.md
        retention-days: 90

    - name: Comment performance summary
      uses: actions/github-script@v7
      if: github.event_name == 'pull_request'
      with:
        script: |
          const fs = require('fs');
          
          let comment = `## 🚀 Performance Test Summary
          
          Performance testing completed for this PR.
          
          ### Results Available:
          - 📦 Bundle size analysis
          - 🔍 Lighthouse audit
          - ⚡ Load testing results
          - 🗄️ Database performance metrics
          
          Check the Actions tab for detailed results and artifacts.
          `;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

    - name: Notify performance team
      uses: slackapi/slack-github-action@v1.26.0
      with:
        channel-id: 'performance'
        payload: |
          {
            "text": "Performance testing completed",
            "attachments": [
              {
                "color": "${{ job.status == 'success' && 'good' || 'danger' }}",
                "fields": [
                  {
                    "title": "Environment",
                    "value": "${{ github.event.inputs.environment || 'staging' }}",
                    "short": true
                  },
                  {
                    "title": "Duration",
                    "value": "${{ github.event.inputs.duration || '10' }} minutes",
                    "short": true
                  },
                  {
                    "title": "Status",
                    "value": "${{ job.status }}",
                    "short": true
                  }
                ]
              }
            ]
          }
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
      if: always()
